2025-08-15 14:56:44,501 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 14:56:44,503 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-15 14:56:44,504 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 14:56:44,506 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-15 14:56:44,506 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:56:44,554 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-15 14:56:44,555 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-15 14:56:44,555 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:56:44,604 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: connecting to localhost:9092 [('127.0.0.1', 9092) IPv4]
2025-08-15 14:56:44,606 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]> returned error 61. Disconnecting.
2025-08-15 14:56:44,606 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:56:44,606 - kafka_consumer_monitor.core.kafka_client - ERROR - 无法连接到Kafka集群，请检查bootstrap_servers配置
2025-08-15 14:56:44,606 - kafka_consumer_monitor.core.consumer_group_monitor - ERROR - 启动监控器失败: NoBrokersAvailable
2025-08-15 14:56:44,606 - __main__ - ERROR - 启动监控程序失败: NoBrokersAvailable
2025-08-15 14:59:31,961 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 14:59:31,966 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-15 14:59:31,966 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 14:59:31,967 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-15 14:59:31,967 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:59:32,016 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-15 14:59:32,018 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-15 14:59:32,018 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:59:32,066 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: connecting to localhost:9092 [('127.0.0.1', 9092) IPv4]
2025-08-15 14:59:32,067 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]> returned error 61. Disconnecting.
2025-08-15 14:59:32,067 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-15 14:59:32,067 - kafka_consumer_monitor.core.kafka_client - ERROR - 无法连接到Kafka集群，请检查bootstrap_servers配置
2025-08-15 14:59:32,067 - kafka_consumer_monitor.core.consumer_group_monitor - ERROR - 启动监控器失败: NoBrokersAvailable
2025-08-15 14:59:32,067 - __main__ - ERROR - 启动监控程序失败: NoBrokersAvailable
2025-08-15 15:42:32,307 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 15:42:32,312 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:42:32,312 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:42:32,328 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:42:32,781 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:42:32,781 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:42:32,782 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:42:32,905 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:42:32,905 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:42:32,955 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:42:32,956 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 15:42:33,014 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:42:33,015 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:42:33,148 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:42:33,149 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:42:33,149 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:42:33,149 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:42:33,166 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:42:33,291 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:42:33,291 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:42:33,291 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 15:42:33,292 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 15:42:33,292 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 15:42:33,292 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 15:42:33,344 - kafka_consumer_monitor.core.kafka_client - ERROR - 获取消费者组列表失败: 'tuple' object has no attribute 'group_id'
2025-08-15 15:42:55,655 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 15:42:55,657 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:42:55,657 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 15:43:00,896 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 15:43:00,896 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:43:08,391 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 15:43:08,392 - __main__ - INFO - Kafka消费者组监控程序已停止
2025-08-15 15:43:18,622 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 15:43:18,625 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:43:18,625 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:43:18,660 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:43:18,817 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:43:18,817 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:43:18,817 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:43:18,936 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:43:18,936 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:43:19,033 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:43:19,033 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 15:43:19,053 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:43:19,054 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:43:19,198 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:43:19,198 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:43:19,199 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:43:19,200 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:43:19,234 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:43:19,365 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:43:19,365 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:43:19,366 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 15:43:19,366 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 15:43:19,367 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 15:43:19,367 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 15:43:19,400 - kafka_consumer_monitor.core.kafka_client - ERROR - 获取消费者组列表失败: 'tuple' object has no attribute 'group_id'
2025-08-15 15:43:23,127 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 15:43:23,127 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:43:23,127 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 15:43:33,131 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:43:33,132 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:43:33,133 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接已关闭
2025-08-15 15:43:33,133 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器已停止
2025-08-15 15:43:39,425 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 15:43:39,425 - __main__ - INFO - Kafka消费者组监控程序已停止
2025-08-15 15:44:31,044 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 15:44:31,049 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:44:31,049 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:44:31,077 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:44:31,198 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:44:31,198 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:44:31,198 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:44:31,325 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:44:31,326 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:44:31,424 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:44:31,425 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 15:44:31,503 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:44:31,503 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:44:31,628 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:44:31,628 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:44:31,629 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:44:31,629 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:44:31,700 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:44:31,829 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:44:31,829 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:44:31,830 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 15:44:31,831 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 15:44:31,832 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 15:44:31,832 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 15:44:31,868 - kafka_consumer_monitor.core.kafka_client - ERROR - 获取消费者组列表失败: 'tuple' object has no attribute 'group_id'
2025-08-15 15:45:01,901 - kafka_consumer_monitor.core.kafka_client - ERROR - 获取消费者组列表失败: 'tuple' object has no attribute 'group_id'
2025-08-15 15:45:31,860 - kafka_consumer_monitor.core.kafka_client - ERROR - 获取消费者组列表失败: 'tuple' object has no attribute 'group_id'
2025-08-15 15:45:36,600 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 15:45:36,601 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:45:36,601 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 15:45:46,608 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:45:46,609 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:45:46,610 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接已关闭
2025-08-15 15:45:46,610 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器已停止
2025-08-15 15:45:52,056 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 15:45:52,057 - __main__ - INFO - Kafka消费者组监控程序已停止
2025-08-15 15:47:33,668 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 15:47:33,672 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:47:33,672 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:47:33,719 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:47:33,869 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:47:33,869 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:47:33,869 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:47:34,000 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:47:34,000 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:47:34,119 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:47:34,120 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 15:47:34,150 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:47:34,150 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:47:34,280 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:47:34,280 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:47:34,281 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:47:34,281 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:47:34,326 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:47:34,479 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:47:34,480 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:47:34,480 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 15:47:34,482 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 15:47:34,483 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 15:47:34,483 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 15:47:34,681 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye_tracer_msg_cloud_new_group 不存在
2025-08-15 15:47:34,832 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye-data-send-monitor-record-group 不存在
2025-08-15 15:47:34,922 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye_record_msg_cloud_group 不存在
2025-08-15 15:47:36,488 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:47:36,489 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 15:47:46,491 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:47:46,492 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:47:46,492 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接已关闭
2025-08-15 15:47:46,492 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器已停止
2025-08-15 15:47:54,805 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 15:47:54,806 - __main__ - INFO - Kafka消费者组监控程序已停止
2025-08-15 15:48:27,242 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 15:48:27,247 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:48:27,247 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:48:27,275 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:48:27,407 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:48:27,408 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:48:27,408 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:48:27,530 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:48:27,530 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:48:27,576 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:48:27,577 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 15:48:27,653 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:48:27,654 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:48:27,779 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:48:27,780 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:48:27,781 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 15:48:27,781 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 15:48:27,817 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 15:48:27,939 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 15:48:27,939 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 15:48:27,940 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 15:48:27,940 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 15:48:27,941 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 15:48:27,941 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 15:48:28,047 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye_record_msg_cloud_group 不存在
2025-08-15 15:48:28,154 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye-data-send-monitor-record-group 不存在
2025-08-15 15:48:28,210 - kafka_consumer_monitor.core.kafka_client - WARNING - 消费者组 moye_tracer_msg_cloud_new_group 不存在
2025-08-15 15:48:35,613 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 15:48:35,614 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 15:48:35,614 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 15:48:45,616 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:48:45,618 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 15:48:45,618 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接已关闭
2025-08-15 15:48:45,618 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器已停止
2025-08-15 15:48:53,006 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 15:48:53,006 - __main__ - INFO - Kafka消费者组监控程序已停止
