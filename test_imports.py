#!/usr/bin/env python3
"""
测试所有模块导入是否正常

这个脚本用于验证所有依赖和模块都能正确导入
"""

def test_imports():
    """测试所有模块导入"""
    print("开始测试模块导入...")
    
    try:
        # 测试基础依赖
        import yaml
        print("✅ PyYAML 导入成功")
        
        import colorama
        print("✅ colorama 导入成功")
        
        import tabulate
        print("✅ tabulate 导入成功")
        
        import jinja2
        print("✅ Jinja2 导入成功")
        
        import click
        print("✅ click 导入成功")
        
        # 测试Kafka客户端
        import kafka
        print("✅ kafka-python-ng 导入成功")
        
        # 测试项目模块
        from kafka_consumer_monitor.config.config_loader import ConfigLoader
        print("✅ ConfigLoader 导入成功")
        
        from kafka_consumer_monitor.core.kafka_client import KafkaClient
        print("✅ KafkaClient 导入成功")
        
        from kafka_consumer_monitor.core.consumer_group_monitor import ConsumerGroupMonitor
        print("✅ ConsumerGroupMonitor 导入成功")
        
        from kafka_consumer_monitor.core.lag_calculator import LagCalculator
        print("✅ LagCalculator 导入成功")
        
        from kafka_consumer_monitor.core.event_tracker import EventTracker
        print("✅ EventTracker 导入成功")
        
        from kafka_consumer_monitor.models.consumer_group import ConsumerGroup, ConsumerGroupState
        print("✅ ConsumerGroup 模型导入成功")
        
        from kafka_consumer_monitor.models.partition_info import PartitionInfo
        print("✅ PartitionInfo 模型导入成功")
        
        from kafka_consumer_monitor.models.event import Event, EventType
        print("✅ Event 模型导入成功")
        
        from kafka_consumer_monitor.reporters.console_reporter import ConsoleReporter
        print("✅ ConsoleReporter 导入成功")
        
        from kafka_consumer_monitor.reporters.file_reporter import FileReporter
        print("✅ FileReporter 导入成功")
        
        from kafka_consumer_monitor.reporters.alert_manager import AlertManager
        print("✅ AlertManager 导入成功")
        
        from kafka_consumer_monitor.utils.logger import setup_logging
        print("✅ Logger 工具导入成功")
        
        from kafka_consumer_monitor.utils.formatter import format_consumer_group_table
        print("✅ Formatter 工具导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n开始测试配置加载...")
    
    try:
        from kafka_consumer_monitor.config.config_loader import ConfigLoader
        
        # 测试默认配置加载
        config_loader = ConfigLoader()
        print("✅ 默认配置加载成功")
        
        # 测试配置获取
        kafka_config = config_loader.get_kafka_config()
        print(f"✅ Kafka配置获取成功: {kafka_config.get('bootstrap_servers', 'N/A')}")
        
        monitoring_config = config_loader.get_monitoring_config()
        print(f"✅ 监控配置获取成功: 间隔 {monitoring_config.get('interval', 'N/A')}秒")
        
        alerts_config = config_loader.get_alerts_config()
        print(f"✅ 告警配置获取成功: 阈值 {alerts_config.get('lag_threshold', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False


def test_data_models():
    """测试数据模型"""
    print("\n开始测试数据模型...")
    
    try:
        from kafka_consumer_monitor.models.consumer_group import ConsumerGroup, ConsumerGroupState, ConsumerMember
        from kafka_consumer_monitor.models.partition_info import PartitionInfo
        from kafka_consumer_monitor.models.event import Event, EventType, EventSeverity
        from datetime import datetime
        
        # 测试分区信息模型
        partition = PartitionInfo(
            topic="test-topic",
            partition=0,
            current_offset=100,
            latest_offset=150,
            lag=None,  # 将自动计算
            consumer_id="test-consumer",
            client_id="test-client",
            host="localhost",
            last_updated=datetime.now()
        )
        print(f"✅ PartitionInfo 创建成功: lag={partition.lag}")
        
        # 测试消费者成员模型
        member = ConsumerMember(
            member_id="test-member",
            client_id="test-client",
            client_host="localhost",
            member_metadata=None,
            member_assignment=None
        )
        print("✅ ConsumerMember 创建成功")
        
        # 测试消费者组模型
        consumer_group = ConsumerGroup(
            group_id="test-group",
            state=ConsumerGroupState.STABLE,
            protocol_type="consumer",
            protocol="range",
            members=[member],
            partitions=[partition],
            coordinator_id=1,
            last_updated=datetime.now()
        )
        print(f"✅ ConsumerGroup 创建成功: 总积压={consumer_group.total_lag}")
        
        # 测试事件模型
        event = Event(
            event_type=EventType.GROUP_STATE_CHANGE,
            group_id="test-group",
            severity=EventSeverity.INFO,
            message="测试事件",
            details={"test": "data"}
        )
        print(f"✅ Event 创建成功: {event.event_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


if __name__ == "__main__":
    print("Kafka消费者组监控程序 - 模块导入测试")
    print("=" * 50)
    
    success = True
    
    # 运行所有测试
    success &= test_imports()
    success &= test_config_loading()
    success &= test_data_models()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！程序已准备就绪。")
        print("\n使用方法:")
        print("1. 修改 config.example.yaml 为 config.yaml 并配置Kafka连接信息")
        print("2. 运行: python main.py -c config.yaml start")
    else:
        print("❌ 部分测试失败，请检查依赖安装。")
    
    exit(0 if success else 1)
